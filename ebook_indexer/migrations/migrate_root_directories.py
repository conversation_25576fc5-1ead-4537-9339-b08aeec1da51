#!/usr/bin/env python3
"""
Migration script to move root directories from config.yaml to database.

This script:
1. Reads root directories from the current config.yaml file
2. Creates RootDirectory documents in the database
3. Optionally removes root_directories from config.yaml (with backup)
"""

import os
import sys
import yaml
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Optional

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from ebook_indexer.database.connection import init_connection, get_connection
from ebook_indexer.database.repository import RootDirectoryRepository
from ebook_indexer.database.models import RootDirectory, IndexingStatus
from ebook_indexer.config.settings import get_config


def setup_logging():
    """Setup logging for migration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('migration.log')
        ]
    )
    return logging.getLogger(__name__)


def backup_config_file(config_path: str) -> str:
    """
    Create a backup of the config file.
    
    Args:
        config_path: Path to the config file
        
    Returns:
        Path to the backup file
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{config_path}.backup_{timestamp}"
    
    with open(config_path, 'r') as src, open(backup_path, 'w') as dst:
        dst.write(src.read())
    
    return backup_path


def load_root_directories_from_config(config_path: str) -> List[str]:
    """
    Load root directories from config file.
    
    Args:
        config_path: Path to the config file
        
    Returns:
        List of root directory paths
    """
    if not os.path.exists(config_path):
        return []
    
    with open(config_path, 'r') as f:
        config_data = yaml.safe_load(f)
    
    return config_data.get('root_directories', [])


def migrate_root_directories(config_path: str, remove_from_config: bool = False) -> bool:
    """
    Migrate root directories from config file to database.
    
    Args:
        config_path: Path to the config file
        remove_from_config: Whether to remove root_directories from config file
        
    Returns:
        True if migration was successful
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Load root directories from config
        root_dirs = load_root_directories_from_config(config_path)
        
        if not root_dirs:
            logger.info("No root directories found in config file")
            return True
        
        logger.info(f"Found {len(root_dirs)} root directories in config file")
        
        # Initialize database connection
        config = get_config(config_path)
        connection = init_connection(config.mongodb_url, config.database_name)
        
        # Test connection
        health = connection.health_check()
        if not health['connected']:
            logger.error(f"Database connection failed: {health['error']}")
            return False
        
        # Initialize repository
        repo = RootDirectoryRepository()
        
        # Migrate each root directory
        migrated_count = 0
        for root_dir_path in root_dirs:
            try:
                # Check if directory already exists in database
                existing = repo.get_root_directory_by_path(root_dir_path)
                if existing:
                    logger.info(f"Root directory already exists in database: {root_dir_path}")
                    continue
                
                # Create new root directory document
                root_dir = RootDirectory(
                    path=root_dir_path,
                    indexing_status=IndexingStatus.NOT_INDEXED
                )
                
                # Validate that the directory exists
                if not os.path.exists(root_dir_path):
                    logger.warning(f"Directory does not exist: {root_dir_path}")
                    # Still save it but mark as error
                    root_dir.indexing_status = IndexingStatus.ERROR
                    root_dir.error_message = "Directory does not exist"
                
                # Save to database
                doc_id = repo.save_root_directory(root_dir)
                logger.info(f"Migrated root directory: {root_dir_path} (ID: {doc_id})")
                migrated_count += 1
                
            except Exception as e:
                logger.error(f"Failed to migrate root directory {root_dir_path}: {e}")
                continue
        
        logger.info(f"Successfully migrated {migrated_count} root directories")
        
        # Optionally remove from config file
        if remove_from_config and migrated_count > 0:
            backup_path = backup_config_file(config_path)
            logger.info(f"Created backup: {backup_path}")
            
            # Remove root_directories from config
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
            
            if 'root_directories' in config_data:
                del config_data['root_directories']
                
                with open(config_path, 'w') as f:
                    yaml.safe_dump(config_data, f, default_flow_style=False, sort_keys=False)
                
                logger.info("Removed root_directories from config file")
        
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return False


def main():
    """Main migration function."""
    logger = setup_logging()
    
    # Get config file path
    config_path = os.path.join(project_root, "config.yaml")
    
    if not os.path.exists(config_path):
        logger.error(f"Config file not found: {config_path}")
        sys.exit(1)
    
    logger.info(f"Starting migration from config file: {config_path}")
    
    # Ask user if they want to remove from config
    remove_from_config = False
    if len(sys.argv) > 1 and sys.argv[1] == "--remove-from-config":
        remove_from_config = True
        logger.info("Will remove root_directories from config file after migration")
    
    # Run migration
    success = migrate_root_directories(config_path, remove_from_config)
    
    if success:
        logger.info("Migration completed successfully")
        sys.exit(0)
    else:
        logger.error("Migration failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
