'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Plus, 
  FolderOpen, 
  BookOpen, 
  AlertTriangle, 
  Trash2,
  CheckCircle,
  XCircle,
  Loader2
} from "lucide-react";
import { 
  getRootDirectories, 
  addRootDirectory, 
  removeRootDirectory,
  RootDirectoryInfo,
  RootDirectoriesResponse 
} from "@/lib/api";
import { formatDate } from "@/lib/utils";
import { AddDirectoryDialog } from "./AddDirectoryDialog";

interface RootDirectoriesCardProps {
  onDirectoryAdded?: () => void;
}

export function RootDirectoriesCard({ onDirectoryAdded }: RootDirectoriesCardProps) {
  const [rootDirs, setRootDirs] = useState<RootDirectoriesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [removingDir, setRemovingDir] = useState<string | null>(null);

  const fetchRootDirectories = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getRootDirectories();
      setRootDirs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch root directories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRootDirectories();
  }, []);

  const handleAddDirectory = async (directoryPath: string) => {
    try {
      await addRootDirectory({ directory_path: directoryPath });
      await fetchRootDirectories();
      onDirectoryAdded?.();
      setShowAddDialog(false);
    } catch (err) {
      throw err; // Let the dialog handle the error
    }
  };

  const handleRemoveDirectory = async (directoryPath: string) => {
    try {
      setRemovingDir(directoryPath);
      await removeRootDirectory({ directory_path: directoryPath });
      await fetchRootDirectories();
      onDirectoryAdded?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove directory');
    } finally {
      setRemovingDir(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Root Directories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Root Directories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={fetchRootDirectories} 
            variant="outline" 
            size="sm" 
            className="mt-4"
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5" />
              Root Directories
              <Badge variant="secondary">{rootDirs?.total_directories || 0}</Badge>
            </CardTitle>
            <Button 
              onClick={() => setShowAddDialog(true)}
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Directory
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {!rootDirs?.root_directories.length ? (
            <div className="text-center py-8 text-muted-foreground">
              <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No root directories configured</p>
              <p className="text-sm">Click "Add Directory" to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {rootDirs.root_directories.map((dir) => (
                <DirectoryItem
                  key={dir.path}
                  directory={dir}
                  onRemove={handleRemoveDirectory}
                  isRemoving={removingDir === dir.path}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <AddDirectoryDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onAdd={handleAddDirectory}
      />
    </>
  );
}

interface DirectoryItemProps {
  directory: RootDirectoryInfo;
  onRemove: (path: string) => void;
  isRemoving: boolean;
}

function DirectoryItem({ directory, onRemove, isRemoving }: DirectoryItemProps) {
  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            {directory.exists ? (
              <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
            )}
            <code className="text-sm font-mono bg-muted px-2 py-1 rounded truncate">
              {directory.path}
            </code>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-1">
              <BookOpen className="h-4 w-4 text-blue-600" />
              <span className="font-medium">{directory.books_count}</span>
              <span className="text-muted-foreground">books</span>
            </div>
            <div className="flex items-center gap-1">
              <FolderOpen className="h-4 w-4 text-green-600" />
              <span className="font-medium">{directory.collections_count}</span>
              <span className="text-muted-foreground">collections</span>
            </div>
            <div className="flex items-center gap-1">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="font-medium">{directory.anomalies_count}</span>
              <span className="text-muted-foreground">anomalies</span>
            </div>
          </div>
          
          {directory.last_scanned && (
            <p className="text-xs text-muted-foreground mt-2">
              Last scanned: {formatDate(directory.last_scanned)}
            </p>
          )}
        </div>
        
        <Button
          onClick={() => onRemove(directory.path)}
          variant="ghost"
          size="sm"
          disabled={isRemoving}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          {isRemoving ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Trash2 className="h-4 w-4" />
          )}
        </Button>
      </div>
      
      {!directory.exists && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            Directory does not exist or is not accessible
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
