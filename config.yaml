mongodb:
  url: mongodb://localhost:27017
  database: ebook_indexer
root_directories:
- /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
- /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403
- /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404
- /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202405
- /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202406
- /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202407
processing:
  max_workers: 4
  batch_size: 100
  retry_attempts: 3
  retry_delay: 60
supported_formats:
- .pdf
- .epub
- .mobi
- .azw
- .azw3
- .txt
anomaly_detection:
  max_nesting_depth: 4
  enforce_naming_convention: true
  detect_misplaced_files: true
  severity_thresholds:
    wrong_level: medium
    deep_nesting: low
    missing_directory: high
    naming_violation: low
logging:
  level: INFO
  file: ebook_indexer.log
  rotation: daily
