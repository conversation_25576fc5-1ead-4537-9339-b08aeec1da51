"""Configuration API router for managing root directories and settings."""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import logging
import os
import yaml
from pathlib import Path
from datetime import datetime

from ..dependencies import get_app_config, get_book_repository
from ..models.requests import AddRootDirectoryRequest, RemoveRootDirectoryRequest
from ..models.responses import (
    BaseResponse,
    RootDirectoriesResponse,
    RootDirectoryInfo,
    ConfigurationResponse
)
from ebook_indexer.database.repository import BookRepository
from ebook_indexer.config.settings import AppConfig


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/root-directories", response_model=RootDirectoriesResponse)
async def get_root_directories_with_stats(
    config: AppConfig = Depends(get_app_config),
    book_repo: BookRepository = Depends(get_book_repository)
) -> RootDirectoriesResponse:
    """Get root directories with statistics."""
    try:
        root_dirs_info = []
        
        for directory in config.root_directories:
            # Check if directory exists
            exists = os.path.exists(directory)
            
            # Get statistics for this directory
            books = book_repo.get_all_books()
            books_in_dir = [book for book in books if book.file_info.file_path.startswith(directory)]
            
            # Count collections in this directory
            collections = set()
            anomalies_count = 0
            last_scanned = None
            
            for book in books_in_dir:
                if book.structure_info and book.structure_info.collection_name:
                    collections.add(book.structure_info.collection_name)
                
                anomalies_count += len(book.anomalies)
                
                if book.processing and book.processing.last_processed_date:
                    if not last_scanned or book.processing.last_processed_date > last_scanned:
                        last_scanned = book.processing.last_processed_date
            
            root_dirs_info.append(RootDirectoryInfo(
                path=directory,
                exists=exists,
                books_count=len(books_in_dir),
                collections_count=len(collections),
                anomalies_count=anomalies_count,
                last_scanned=last_scanned
            ))
        
        return RootDirectoriesResponse(
            root_directories=root_dirs_info,
            total_directories=len(config.root_directories)
        )
        
    except Exception as e:
        logger.error(f"Failed to get root directories with stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get root directories: {e}")


@router.post("/root-directories", response_model=BaseResponse)
async def add_root_directory(
    request: AddRootDirectoryRequest,
    config: AppConfig = Depends(get_app_config)
) -> BaseResponse:
    """Add a new root directory to the configuration."""
    try:
        # Check if directory exists
        if not os.path.exists(request.directory_path):
            raise HTTPException(
                status_code=400,
                detail=f"Directory does not exist: {request.directory_path}"
            )
        
        # Check if directory is already in the list
        if request.directory_path in config.root_directories:
            raise HTTPException(
                status_code=400,
                detail=f"Directory already exists in configuration: {request.directory_path}"
            )
        
        # Get the config file path from main app
        from ..main import get_config_file_path, reload_app_config
        config_file_path = get_config_file_path()

        if not config_file_path or not os.path.exists(config_file_path):
            raise HTTPException(
                status_code=500,
                detail="Configuration file not found"
            )
        
        # Read current config
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # Add new directory
        if 'root_directories' not in config_data:
            config_data['root_directories'] = []
        
        config_data['root_directories'].append(request.directory_path)
        
        # Write updated config
        with open(config_file_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(config_data, f, default_flow_style=False, sort_keys=False)
        
        logger.info(f"Added root directory: {request.directory_path}")
        
        return BaseResponse(
            success=True,
            message=f"Successfully added root directory: {request.directory_path}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add root directory: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add root directory: {e}")


@router.delete("/root-directories", response_model=BaseResponse)
async def remove_root_directory(
    request: RemoveRootDirectoryRequest,
    config: AppConfig = Depends(get_app_config)
) -> BaseResponse:
    """Remove a root directory from the configuration."""
    try:
        # Check if directory is in the list
        if request.directory_path not in config.root_directories:
            raise HTTPException(
                status_code=404,
                detail=f"Directory not found in configuration: {request.directory_path}"
            )
        
        # Update the configuration file
        config_file_path = "config.yaml"  # Default config file
        if not os.path.exists(config_file_path):
            raise HTTPException(
                status_code=500,
                detail="Configuration file not found"
            )
        
        # Read current config
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # Remove directory
        if 'root_directories' in config_data:
            config_data['root_directories'] = [
                d for d in config_data['root_directories'] 
                if d != request.directory_path
            ]
        
        # Write updated config
        with open(config_file_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(config_data, f, default_flow_style=False, sort_keys=False)
        
        logger.info(f"Removed root directory: {request.directory_path}")
        
        return BaseResponse(
            success=True,
            message=f"Successfully removed root directory: {request.directory_path}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove root directory: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to remove root directory: {e}")


@router.get("/current", response_model=ConfigurationResponse)
async def get_current_configuration(
    config: AppConfig = Depends(get_app_config)
) -> ConfigurationResponse:
    """Get current configuration settings."""
    try:
        return ConfigurationResponse(
            root_directories=config.root_directories,
            supported_extensions=config.supported_extensions,
            max_workers=config.max_workers,
            batch_size=config.batch_size,
            anomaly_detection=config.anomaly_detection
        )
        
    except Exception as e:
        logger.error(f"Failed to get current configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {e}")
