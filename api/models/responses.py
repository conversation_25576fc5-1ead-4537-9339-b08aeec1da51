"""API response models."""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


# Base response models
class PaginationInfo(BaseModel):
    """Pagination information."""
    total: int
    page: int
    per_page: int
    pages: int


class BaseResponse(BaseModel):
    """Base API response."""
    success: bool = True
    message: Optional[str] = None


# Dashboard models
class DashboardSummary(BaseModel):
    """Dashboard summary statistics."""
    total_books: int
    total_collections: int
    total_anomalies: int
    active_jobs: int
    last_scan_date: Optional[datetime]


class RecentActivity(BaseModel):
    """Recent activity item."""
    job_id: str
    status: str
    files_processed: int
    anomalies_found: int
    duration: str
    created_date: datetime


class AnomalySummaryStats(BaseModel):
    """Anomaly summary statistics."""
    total_anomalies: int
    by_severity: Dict[str, int]
    by_type: Dict[str, int]
    resolved: int
    unresolved: int


class ProcessingStats(BaseModel):
    """Processing performance statistics."""
    files_per_hour: float
    success_rate: float
    avg_processing_time_ms: float


class DashboardOverview(BaseModel):
    """Complete dashboard overview."""
    summary: DashboardSummary
    recent_activity: List[RecentActivity]
    anomaly_summary: AnomalySummaryStats
    processing_stats: ProcessingStats


# Job models
class JobStatistics(BaseModel):
    """Job statistics."""
    total_files: int = 0
    processed_files: int = 0
    successful_files: int = 0
    failed_files: int = 0
    anomalous_files: int = 0
    collections_found: int = 0
    processing_rate_files_per_sec: float = 0.0


class JobTiming(BaseModel):
    """Job timing information."""
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    duration_seconds: Optional[int]
    estimated_completion: Optional[datetime]


class JobResponse(BaseModel):
    """Job response model."""
    id: str
    status: str
    root_directories: List[str]
    statistics: JobStatistics
    timing: JobTiming
    created_date: datetime


class JobListResponse(BaseModel):
    """Job list response."""
    jobs: List[JobResponse]
    pagination: PaginationInfo


# Book models
class FileInfo(BaseModel):
    """File information."""
    file_path: str
    filename: str
    file_size: int
    last_modified: datetime


class BookMetadata(BaseModel):
    """Book metadata."""
    title: Optional[str]
    author: Optional[str]
    format: Optional[str]
    pages: Optional[int]
    publisher: Optional[str]
    isbn: Optional[str]
    language: Optional[str]
    description: Optional[str]


class StructureInfo(BaseModel):
    """File structure information."""
    collection_name: Optional[str]
    book_directory: Optional[str]
    nesting_level: int
    follows_convention: bool


class ProcessingInfo(BaseModel):
    """Processing information."""
    status: str
    job_id: Optional[str]
    processing_time_ms: Optional[int]
    retry_count: int = 0
    last_processed_date: Optional[datetime]


class AnomalyInfo(BaseModel):
    """Anomaly information."""
    type: str
    severity: str
    description: str
    suggested_action: Optional[str]
    resolved: bool = False
    detected_date: datetime


class BookResponse(BaseModel):
    """Book response model."""
    id: str
    file_info: FileInfo
    metadata: BookMetadata
    structure_info: StructureInfo
    processing: ProcessingInfo
    anomalies: List[AnomalyInfo]
    created_date: datetime


class BookListResponse(BaseModel):
    """Book list response."""
    books: List[BookResponse]
    pagination: PaginationInfo
    filters_applied: Dict[str, Any]


class CollectionInfo(BaseModel):
    """Collection information."""
    name: str
    book_count: int
    total_size_bytes: int
    anomaly_count: int
    last_updated: datetime


class CollectionListResponse(BaseModel):
    """Collection list response."""
    collections: List[CollectionInfo]


# Anomaly models
class AnomalyResponse(BaseModel):
    """Anomaly response model."""
    id: str
    book_id: str
    file_path: str
    anomaly_type: str
    severity: str
    description: str
    suggested_action: Optional[str]
    resolved: bool
    detected_date: datetime
    collection_name: Optional[str]


class AnomalyListResponse(BaseModel):
    """Anomaly list response."""
    anomalies: List[AnomalyResponse]
    summary: AnomalySummaryStats
    pagination: PaginationInfo


# Operation models
class OperationProgress(BaseModel):
    """Operation progress information."""
    processed: int
    total: int
    percentage: float
    eta_seconds: Optional[int]
    current_item: Optional[str]


class OperationResults(BaseModel):
    """Operation results."""
    successful: int = 0
    failed: int = 0
    still_anomalous: int = 0


class OperationStatus(BaseModel):
    """Operation status response."""
    operation_id: str
    type: str
    status: str
    progress: OperationProgress
    results: OperationResults
    started_at: datetime
    estimated_completion: Optional[datetime]


class OperationStartResponse(BaseModel):
    """Operation start response."""
    operation_id: str
    status: str
    estimated_files: int
    message: str


# WebSocket models
class WebSocketMessage(BaseModel):
    """WebSocket message format."""
    type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# System status models
class SystemHealth(BaseModel):
    """System health information."""
    status: str
    database: Dict[str, Any]
    configuration: Dict[str, Any]
    version: str
    timestamp: datetime


class AppState(BaseModel):
    """Complete application state."""
    system_health: SystemHealth
    dashboard_summary: DashboardSummary
    active_operations: List[OperationStatus]
    recent_jobs: List[JobResponse]


# Configuration models
class RootDirectoryInfo(BaseModel):
    """Information about a root directory."""
    path: str = Field(..., description="Directory path")
    exists: bool = Field(..., description="Whether the directory exists")
    books_count: int = Field(default=0, description="Number of books in this directory")
    collections_count: int = Field(default=0, description="Number of collections in this directory")
    anomalies_count: int = Field(default=0, description="Number of anomalies in this directory")
    last_scanned: Optional[datetime] = Field(default=None, description="Last scan date for this directory")


class RootDirectoriesResponse(BaseModel):
    """Response containing root directories information."""
    root_directories: List[RootDirectoryInfo] = Field(..., description="List of root directories with statistics")
    total_directories: int = Field(..., description="Total number of root directories")


class ConfigurationResponse(BaseModel):
    """Response containing current configuration."""
    root_directories: List[str] = Field(..., description="List of root directory paths")
    supported_extensions: List[str] = Field(..., description="Supported file extensions")
    max_workers: int = Field(..., description="Maximum number of workers")
    batch_size: int = Field(..., description="Processing batch size")
    anomaly_detection: Dict[str, Any] = Field(..., description="Anomaly detection settings")


# Error models
class ErrorDetail(BaseModel):
    """Error detail."""
    code: str
    message: str
    field: Optional[str] = None


class ErrorResponse(BaseModel):
    """Error response."""
    success: bool = False
    error: ErrorDetail
    timestamp: datetime = Field(default_factory=datetime.utcnow)
